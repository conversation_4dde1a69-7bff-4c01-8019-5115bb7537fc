import React, { useEffect, useState } from "react";
import { View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useQueryClient } from "react-query";
import { useNavigation, useIsFocused } from "@react-navigation/native";
import { useIsForeground } from "../../../bAudit/src/helpers/useIsForeground";

//Components
import { CustomText, Loader } from "bcomponents";
import ApprovalsList from "../components/approvalsList";

// Redux & queries
import { queryKeys } from "../queries/queryKeys";
import getCategoryApprovals from "../queries/getCategoryApprovals";
import {
  clearLastPerformedActionItem,
  setApprovalsCategoryListFilters,
  setApprovalsListFilters,
  setCategoryApprovals,
  setScrollIndex,
} from "../slices/approvalsSlice";

// Styles
import { FONTSIZES } from "bstyles";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

const SingleCategoryHOC = () => {
  const { color } = useThemeAwareObject((color) => color);
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const isFocused = useIsFocused();
  const isForeground = useIsForeground();
  const isActive = isFocused && isForeground;
  const {
    approvalsCategoryFilters,
    approvalsFilters,
    approvalsListCategory,
    vessels,
    categories,
    expenses,
    isConnectedToNetwork,
    scrollIndex,
    docTypes,
    lastPerformedActionItem,
  } = useSelector((state) => state.root.approvalsReducer);
  const { data, error, isError, isLoading, isFetching } = getCategoryApprovals(
    approvalsCategoryFilters.fleetId || approvalsFilters.fleetId,
    approvalsCategoryFilters.vesselId || approvalsFilters.vesselId,
    approvalsCategoryFilters.categoryId,
    approvalsCategoryFilters.senderId || approvalsFilters.senderId,
    approvalsCategoryFilters.expenseId || approvalsFilters.expenseId
  );
  useEffect(() => {
    if (!isLoading && data) {
      dispatch(setCategoryApprovals(data));
    }
  }, [isLoading, data, queryClient]);

  useEffect(() => {
    return () => {
      dispatch(
        setApprovalsListFilters({
          ...approvalsFilters,
          categoryId: "",
        })
      );
      dispatch(
        setApprovalsCategoryListFilters({
          ...approvalsCategoryFilters,
          categoryId: "",
          vesselId: "",
        })
      );
    };
  }, []);

  useEffect(() => {
    if (isConnectedToNetwork && isActive) {
      queryClient.invalidateQueries(
        queryKeys.categoryApprovals({
          fleetId: approvalsCategoryFilters.fleetId,
          vesselId: approvalsCategoryFilters.vesselId,
          categoryId: approvalsCategoryFilters.categoryId,
          senderId: approvalsCategoryFilters.senderId,
          expenseId: approvalsCategoryFilters.expenseId,
        })
      );
    }
  }, [isConnectedToNetwork, isActive]);

  const [refreshing, setRefreshing] = useState(false);
  const onRefresh = async () => {
    setRefreshing(true);

    await queryClient.invalidateQueries(
      queryKeys.categoryApprovals({
        fleetId: approvalsCategoryFilters.fleetId,
        vesselId: approvalsCategoryFilters.vesselId,
        categoryId: approvalsCategoryFilters.categoryId,
        senderId: approvalsCategoryFilters.senderId,
        expenseId: approvalsCategoryFilters.expenseId,
      })
    );

    setRefreshing(false);
    dispatch(clearLastPerformedActionItem()); // Reset last performed action item on refresh
  };

  const sortedData = [
    {
      title: {
        name: vessels[approvalsCategoryFilters.vesselId]?.vesselName,
        sumOfApprovals: approvalsListCategory?.allIds?.length,
      },
      data: approvalsListCategory?.allIds?.map((id) => {
        return {
          vesselName: vessels[approvalsListCategory[id].vesselId]?.vesselName
            ? vessels[approvalsListCategory[id].vesselId]?.vesselName
            : approvalsListCategory[id].vesselName,
          taskNumber: approvalsListCategory[id]?.taskNumber,
          documentId: approvalsListCategory[id]?.documentId,
          categoryId: categories[approvalsListCategory[id].categoryId].id,
          amount: approvalsListCategory[id]?.amount,
          supplier: approvalsListCategory[id]?.supplier,
          isImportant: approvalsListCategory[id]?.importance,
          status: approvalsListCategory[id]?.status,
          docRef: approvalsListCategory[id]?.docRef,
          expenses:
            approvalsListCategory[id]?.expensesIds?.length > 0
              ? [
                  ...approvalsListCategory[id]?.expensesIds?.map(
                    (expenseId) => expenses[expenseId]?.expenseName
                  ),
                ].join(", ")
              : "",
        };
      }),
    },
  ];

  useEffect(() => {
    navigation.setOptions({
      title: sortedData[0]?.title?.name
        ? `${sortedData[0]?.title?.name} - ${
            docTypes[sortedData[0]?.data[0]?.categoryId]?.DocType
          }`
        : docTypes[sortedData[0]?.data[0]?.categoryId]?.DocType,
    });
  }, [navigation, sortedData]);

  // Place the last performed action item at the top
  if (lastPerformedActionItem) {
    sortedData[0].data = [
      {
        ...lastPerformedActionItem,
        isImportant: lastPerformedActionItem?.importance,
        expenses:
          lastPerformedActionItem?.expensesIds?.length > 0
            ? [
                ...lastPerformedActionItem?.expensesIds?.map(
                  (expenseId) => expenses[expenseId]?.expenseName
                ),
              ].join(", ")
            : "",
      },
      ...sortedData[0].data,
    ];

    dispatch(setScrollIndex(0));
  }

  useEffect(() => {
    return () => {
      dispatch(setScrollIndex(null));
      dispatch(clearLastPerformedActionItem()); // Reset last performed action item on refresh
    };
  }, [navigation]);

  if (isLoading || isFetching) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <Loader />
      </View>
    );
  }

  if (isError) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: color.BACKGROUND,
          padding: 40,
        }}
      >
        <CustomText
          style={{ fontSize: FONTSIZES.TWENTY_FOUR, textAlign: "center" }}
        >
          {error != null ? error?.message : <></>}
        </CustomText>
      </View>
    );
  }

  return (
    <ApprovalsList
      data={sortedData}
      isSummary={false}
      refresh={refreshing}
      onRefresh={onRefresh}
      scrollIndex={scrollIndex}
    />
  );
};

export default SingleCategoryHOC;
