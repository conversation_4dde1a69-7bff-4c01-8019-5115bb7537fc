import axios from "axios";
import { useQuery } from "react-query";
import { queryKeys } from "./queryKeys";
import { useAuthContext } from "../contexts/AuthContext";

const getCategoryApprovals = (
  fleetId,
  vesselId,
  categoryId,
  senderId,
  expenseId
) => {
  const { token } = useAuthContext();
  const baseUrl = useAuthContext().companyInfo.bSuiteUrl;

  const enabledFilters = {
    top: 150,
    skip: 0,
    orderBy: 1,
    updoc_type_id: categoryId ? categoryId : null,
    vsl_id: vesselId ? vesselId : null,
    vsl_grp_id: fleetId ? fleetId : null,
    bgacc_id: expenseId ? expenseId : null,
    sender_id: senderId ? senderId : null,
  };

  const urlEncodedData = Object.entries(enabledFilters)
    .map((e) => e.join("="))
    .join("&");

  const fetchBaseUrl = async () => {
    const { makeAxiosCall } = useAuthContext();

    try {
      const data = await makeAxiosCall(
        `approvals-module/tasks?${urlEncodedData}`
      );

      return data;
    } catch (error) {
      //TODO find the server response
      if (error.response && error.response.status === 500) {
        error.serverResponse = error.response.data; // Add custom property
        throw error; // Re-throw the error to let react-query handle it
      }

      throw error; // Re-throw the error for other scenarios
    }
  };

  const { data, error, isError, isLoading, isFetching } = useQuery(
    queryKeys.categoryApprovals({
      fleetId,
      vesselId,
      categoryId,
      senderId,
      expenseId,
    }),
    fetchBaseUrl
  );

  return {
    data,
    error,
    isError,
    isLoading,
    isFetching,
  };
};

export default getCategoryApprovals;
